{"name": "dr-clinic-app", "productName": "عيادة الدكتور يونس اسود الجبوري", "version": "1.0.0", "description": "نظام إدارة المواعيد الطبية للدكتور يونس اسود الجبوري", "main": "main.js", "homepage": ".", "scripts": {"start": "electron .", "build": "electron-builder", "build:win": "electron-builder --win", "build:win32": "electron-builder --win --ia32", "build:win64": "electron-builder --win --x64", "build:all": "electron-builder --win --ia32 --x64", "dist": "npm run build:all"}, "keywords": ["medical", "clinic", "appointments", "doctor", "healthcare"], "author": {"name": "Dr. <PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"electron": "^22.3.27", "electron-builder": "^24.13.3"}, "build": {"appId": "com.dr-clinic.appointment-manager", "productName": "عيادة الدكتور يونس اسود الجبوري", "directories": {"buildResources": "build"}, "files": ["**/*", "!node_modules/**/*", "node_modules/electron/**/*"], "win": {"target": [{"target": "nsis", "arch": ["x64", "ia32"]}], "icon": "assets/icon.ico", "requestedExecutionLevel": "asInvoker"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "عيادة الدكتور يونس", "include": "build/installer.nsh"}}}