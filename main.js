const { app, BrowserWindow } = require('electron');
const path = require('path');

let mainWindow;
let splashWindow;

function createWindows() {
  // 1) إنشاء نافذة Splash
  splashWindow = new BrowserWindow({
    width: 400,
    height: 300,
    frame: false,         // إخفاء شريط العنوان
    alwaysOnTop: true,    // تكون في المقدمة دائماً
    transparent: true,    // خلفية شفافة
  });

  // عرض ملف splash.html
  splashWindow.loadFile('splash.html');

  // 2) إنشاء النافذة الرئيسية (تطبيقك الأساسي)
  mainWindow = new BrowserWindow({
    width: 800,
    height: 600,
    show: false, // إخفاء النافذة إلى أن تكون جاهزة للعرض
    autoHideMenuBar: true,
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false
    }
  });

  // عرض ملف index.html
  mainWindow.loadFile('index.html');

  // عند جهوزية النافذة الرئيسية، أخفِ الـ Splash وأظهِر النافذة الأساسية
  mainWindow.once('ready-to-show', () => {
    if (splashWindow) {
      splashWindow.destroy(); // إغلاق نافذة Splash
    }
    mainWindow.show();        // إظهار النافذة الرئيسية
  });
}

app.whenReady().then(() => {
  // تعطيل بعض الميزات لتقليل التحميل على الأجهزة القديمة وضمان التوافق مع Windows 7
  app.commandLine.appendSwitch('disable-gpu');
  app.commandLine.appendSwitch('disable-software-rasterizer');
  app.commandLine.appendSwitch('disable-gpu-sandbox');
  app.commandLine.appendSwitch('disable-web-security');
  app.commandLine.appendSwitch('disable-features', 'VizDisplayCompositor');

  // ضمان التوافق مع Windows 7
  if (process.platform === 'win32') {
    app.commandLine.appendSwitch('high-dpi-support', 'true');
    app.commandLine.appendSwitch('force-device-scale-factor', '1');
  }

  createWindows();

  // في حالة كان التطبيق يعمل على macOS وتحتاج لإعادة إنشاء النافذة عند النقر على الأيقونة Dock
  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      createWindows();
    }
  });
});

// إغلاق التطبيق عند إغلاق كل النوافذ (على أنظمة غير macOS)
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});
