; Custom NSIS installer script for Dr. <PERSON> App
; Simple installer configuration for Windows compatibility

; Custom finish page
!define MUI_FINISHPAGE_RUN "$INSTDIR\${PRODUCT_FILENAME}"
!define MUI_FINISHPAGE_RUN_TEXT "تشغيل التطبيق الآن"

; Installation directory
InstallDir "$PROGRAMFILES\Dr Clinic App"

; Registry key for uninstaller
WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\DrClinicApp" "DisplayName" "عيادة الدكتور يونس اسود الجبوري"
WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\DrClinicApp" "UninstallString" "$INSTDIR\Uninstall.exe"
WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\DrClinicApp" "DisplayIcon" "$INSTDIR\${PRODUCT_FILENAME}"
WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\DrClinicApp" "Publisher" "Dr. You<PERSON> Aswad Al-Jubouri Clinic"
WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\DrClinicApp" "DisplayVersion" "${VERSION}"

; Create desktop shortcut
CreateShortCut "$DESKTOP\Dr Clinic App.lnk" "$INSTDIR\${PRODUCT_FILENAME}" "" "$INSTDIR\${PRODUCT_FILENAME}" 0

; Create start menu shortcuts
CreateDirectory "$SMPROGRAMS\Dr Clinic App"
CreateShortCut "$SMPROGRAMS\Dr Clinic App\Dr Clinic App.lnk" "$INSTDIR\${PRODUCT_FILENAME}" "" "$INSTDIR\${PRODUCT_FILENAME}" 0
CreateShortCut "$SMPROGRAMS\Dr Clinic App\Uninstall.lnk" "$INSTDIR\Uninstall.exe"
