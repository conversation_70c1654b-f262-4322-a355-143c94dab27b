; Custom NSIS installer script for Dr<PERSON> Clinic App
; This script ensures compatibility with Windows 7, 8, 10, and 11

; Check Windows version compatibility
!include "WinVer.nsh"

Function .onInit
  ; Check if Windows version is supported (Windows 7 or later)
  ${IfNot} ${AtLeastWin7}
    MessageBox MB_OK|MB_ICONSTOP "This application requires Windows 7 or later."
    Abort
  ${EndIf}
FunctionEnd

; Custom installation messages in Arabic
LangString DESC_SecMain ${LANG_ENGLISH} "نظام إدارة المواعيد الطبية"
LangString DESC_SecDesktop ${LANG_ENGLISH} "إنشاء اختصار على سطح المكتب"
LangString DESC_SecStartMenu ${LANG_ENGLISH} "إنشاء اختصار في قائمة ابدأ"

; Set compression
SetCompressor /SOLID lzma
SetCompressorDictSize 64

; Request admin privileges for installation
RequestExecutionLevel admin

; Custom finish page
!define MUI_FINISHPAGE_RUN "$INSTDIR\${PRODUCT_FILENAME}"
!define MUI_FINISHPAGE_RUN_TEXT "تشغيل التطبيق الآن"

; Installation directory
InstallDir "$PROGRAMFILES\Dr Clinic App"

; Registry key for uninstaller
WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\DrClinicApp" "DisplayName" "عيادة الدكتور يونس اسود الجبوري"
WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\DrClinicApp" "UninstallString" "$INSTDIR\Uninstall.exe"
WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\DrClinicApp" "DisplayIcon" "$INSTDIR\${PRODUCT_FILENAME}"
WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\DrClinicApp" "Publisher" "Dr. Younes Aswad Al-Jubouri Clinic"
WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\DrClinicApp" "DisplayVersion" "${VERSION}"

; Create desktop shortcut with Arabic name
CreateShortCut "$DESKTOP\عيادة الدكتور يونس.lnk" "$INSTDIR\${PRODUCT_FILENAME}" "" "$INSTDIR\${PRODUCT_FILENAME}" 0

; Create start menu shortcuts
CreateDirectory "$SMPROGRAMS\عيادة الدكتور يونس"
CreateShortCut "$SMPROGRAMS\عيادة الدكتور يونس\عيادة الدكتور يونس.lnk" "$INSTDIR\${PRODUCT_FILENAME}" "" "$INSTDIR\${PRODUCT_FILENAME}" 0
CreateShortCut "$SMPROGRAMS\عيادة الدكتور يونس\إلغاء التثبيت.lnk" "$INSTDIR\Uninstall.exe"
